{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../src/common.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;AAwZjC,oDAiGC;AAtfD,6BAAwB;AAExB,iCAA2B;AAC3B,oDAA4B;AAG5B;;;;GAIG;AACU,QAAA,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,UAAG,CAAC,IAAI,eAAe,CAAC,CAAC;AAE1E,iEAAiE;AACjE,MAAa,WAAqB,SAAQ,KAAK;IA6B7C;;;;;OAKG;IACH,MAAM,CAAC,OARN,2BAAmB,EAQZ,MAAM,CAAC,WAAW,EAAC,CAAC,QAAiB;QAC3C,IACE,QAAQ;YACR,OAAO,QAAQ,KAAK,QAAQ;YAC5B,2BAAmB,IAAI,QAAQ;YAC/B,QAAQ,CAAC,2BAAmB,CAAC,KAAK,UAAG,CAAC,OAAO,EAC7C,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC5E,CAAC;IAED,YACE,OAAe,EACR,MAAqB,EACrB,QAA4B,EAC5B,KAAqC;;QAE5C,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,WAAM,GAAN,MAAM,CAAe;QACrB,aAAQ,GAAR,QAAQ,CAAoB;QAC5B,UAAK,GAAL,KAAK,CAAgC;QAnC9C;;;;;;;;WAQG;QACH,QAAqB,GAAG,UAAG,CAAC,OAAO,CAAC;QA8BlC,+CAA+C;QAC/C,6CAA6C;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAA,gBAAM,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAA,gBAAM,EAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC;gBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,aAAa,CAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EACxB,MAAA,IAAI,CAAC,QAAQ,0CAAE,IAAI,CACpB,CAAC;YACJ,CAAC;YAAC,WAAM,CAAC;gBACP,qDAAqD;gBACrD,oEAAoE;gBACpE,0DAA0D;YAC5D,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrC,CAAC;QAED,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,MAAM,CAAC,aAAa,CAAI;gBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA1FD,kCA0FC;AAsRD,SAAS,aAAa,CAAC,YAAgC,EAAE,IAAS;IAChE,QAAQ,YAAY,EAAE,CAAC;QACrB,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC;QACd,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1C,KAAK,aAAa;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACxD,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjC;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,oBAAoB,CAAU,IAG7C;IACC,MAAM,MAAM,GACV,0EAA0E,CAAC;IAE7E,SAAS,aAAa,CAAC,OAAiB;QACtC,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,iCAAiC;YACjC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YACxB,CAAC;YAED,gCAAgC;YAChC,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YACxB,CAAC;YAED,sDAAsD;YACtD,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,YAAY,CAAC,GAAkB,EAAE,GAAwB;QAChE,IACE,OAAO,GAAG,KAAK,QAAQ;YACvB,GAAG,KAAK,IAAI;YACZ,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,EAC5B,CAAC;YACD,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAEtB,IACE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;gBACxB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EACpB,CAAC;gBACD,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,YAAY,CAAkC,GAAM;QAC3D,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YAC5C,IAAI,YAAY,IAAI,GAAG,EAAE,CAAC;gBACxB,GAAG,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;YAC7B,CAAC;YAED,IAAI,WAAW,IAAI,GAAG,EAAE,CAAC;gBACvB,GAAG,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;YAC5B,CAAC;YAED,IAAI,eAAe,IAAI,GAAG,EAAE,CAAC;gBAC3B,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE/B,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEzC,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACxC,CAAC;YAED,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC1C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC;QAAC,WAAM,CAAC;YACP,iDAAiD;QACnD,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,oBAAoB,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,CAAC,CAAC;QACrD,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACpC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}