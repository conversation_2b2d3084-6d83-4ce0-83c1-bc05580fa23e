"""
FastAPI routes for Chart APIs
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging

from models.revenue_models import (
    RevenueFilterParams, PieChartResponse, TreemapResponse, ScatterPlotResponse,
    HistogramResponse, ParetoChartResponse, GaugeChartResponse, HeatmapCalendarResponse,
    SankeyChartResponse, RadarChartResponse, BoxPlotResponse, WaterfallChartResponse,
    PieChartItem, TreemapItem, ScatterPlotItem, HistogramItem, ParetoChartItem,
    GaugeChartItem, HeatmapCalendarItem, SankeyChartItem, RadarChartItem,
    BoxPlotItem, WaterfallChartItem
)
from services.chart_service import ChartService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/charts", tags=["charts"])

@router.post("/pie-chart", response_model=PieChartResponse)
async def get_pie_chart(filters: RevenueFilterParams, chart_type: str = Query("payertype", description="Type of pie chart")):
    """
    Get pie chart data for payertype or unit-wise share
    
    Args:
        filters: Revenue filter parameters including unitid
        chart_type: Type of pie chart (payertype, unit, etc.)
        
    Returns:
        Pie chart response with data
    """
    try:
        raw_data, total_value = ChartService.get_pie_chart_data(
            unitid=filters.unitid,
            chart_type=chart_type
        )
        
        # Convert raw data to response model
        pie_items = [
            PieChartItem(
                category=item['category'],
                value=float(item['value']),
                percentage=float(item['percentage'])
            )
            for item in raw_data
        ]
        
        return PieChartResponse(
            data=pie_items,
            total_value=total_value,
            unitid=filters.unitid,
            chart_type=chart_type
        )
        
    except Exception as e:
        logger.error(f"Error in pie chart endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/treemap", response_model=TreemapResponse)
async def get_treemap(filters: RevenueFilterParams):
    """
    Get treemap data for Service Department → Doctor → Net Revenue
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        Treemap response with data
    """
    try:
        raw_data = ChartService.get_treemap_data(unitid=filters.unitid)
        
        # Convert raw data to response model
        treemap_items = [
            TreemapItem(
                service_department=item['service_department'],
                performingdoctor=item['performingdoctor'],
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]
        
        return TreemapResponse(
            data=treemap_items,
            total_records=len(treemap_items),
            unitid=filters.unitid
        )
        
    except Exception as e:
        logger.error(f"Error in treemap endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/scatter-plot", response_model=ScatterPlotResponse)
async def get_scatter_plot(filters: RevenueFilterParams):
    """
    Get scatter plot data for Rate vs Quantity by Service
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        Scatter plot response with data
    """
    try:
        raw_data = ChartService.get_scatter_plot_data(unitid=filters.unitid)
        
        # Convert raw data to response model
        scatter_items = [
            ScatterPlotItem(
                service_name=item['service_name'],
                rate=float(item['rate']),
                quantity=int(item['quantity'])
            )
            for item in raw_data
        ]
        
        return ScatterPlotResponse(
            data=scatter_items,
            total_records=len(scatter_items),
            unitid=filters.unitid
        )
        
    except Exception as e:
        logger.error(f"Error in scatter plot endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/histogram", response_model=HistogramResponse)
async def get_histogram(filters: RevenueFilterParams, 
                       bucket_count: int = Query(10, description="Number of buckets"),
                       max_amount: float = Query(10000, description="Maximum amount to consider")):
    """
    Get histogram data for distribution of net amounts
    
    Args:
        filters: Revenue filter parameters including unitid
        bucket_count: Number of buckets for histogram
        max_amount: Maximum amount to consider
        
    Returns:
        Histogram response with data
    """
    try:
        raw_data = ChartService.get_histogram_data(
            unitid=filters.unitid,
            bucket_count=bucket_count,
            max_amount=max_amount
        )
        
        # Convert raw data to response model
        histogram_items = [
            HistogramItem(
                bucket=int(item['bucket']),
                frequency=int(item['frequency']),
                range_start=float(item['range_start']),
                range_end=float(item['range_end'])
            )
            for item in raw_data
        ]
        
        return HistogramResponse(
            data=histogram_items,
            total_records=len(histogram_items),
            unitid=filters.unitid,
            bucket_count=bucket_count
        )
        
    except Exception as e:
        logger.error(f"Error in histogram endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pareto-chart", response_model=ParetoChartResponse)
async def get_pareto_chart(filters: RevenueFilterParams, 
                          limit: int = Query(20, description="Number of top services")):
    """
    Get pareto chart data for top services contributing to revenue
    
    Args:
        filters: Revenue filter parameters including unitid
        limit: Number of top services to return
        
    Returns:
        Pareto chart response with data
    """
    try:
        raw_data, total_amount = ChartService.get_pareto_chart_data(
            unitid=filters.unitid,
            limit=limit
        )
        
        # Convert raw data to response model
        pareto_items = [
            ParetoChartItem(
                service_name=item['service_name'],
                netamount=float(item['netamount']),
                cumulative_percentage=float(item['cumulative_percentage'])
            )
            for item in raw_data
        ]
        
        return ParetoChartResponse(
            data=pareto_items,
            total_records=len(pareto_items),
            unitid=filters.unitid,
            total_amount=total_amount
        )
        
    except Exception as e:
        logger.error(f"Error in pareto chart endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/gauge-chart", response_model=GaugeChartResponse)
async def get_gauge_chart(filters: RevenueFilterParams, 
                         target_mtd: float = Query(2000000, description="Target MTD amount")):
    """
    Get gauge chart data for current MTD vs target
    
    Args:
        filters: Revenue filter parameters including unitid
        target_mtd: Target MTD amount
        
    Returns:
        Gauge chart response with data
    """
    try:
        gauge_data = ChartService.get_gauge_chart_data(
            unitid=filters.unitid,
            target_mtd=target_mtd
        )
        
        # Convert raw data to response model
        gauge_item = GaugeChartItem(
            current_mtd=float(gauge_data['current_mtd']),
            target_mtd=float(gauge_data['target_mtd']),
            percentage=float(gauge_data['percentage'])
        )
        
        current_date = datetime.now()
        
        return GaugeChartResponse(
            data=gauge_item,
            unitid=filters.unitid,
            month=current_date.month,
            year=current_date.year
        )
        
    except Exception as e:
        logger.error(f"Error in gauge chart endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/heatmap-calendar", response_model=HeatmapCalendarResponse)
async def get_heatmap_calendar(filters: RevenueFilterParams):
    """
    Get heatmap calendar data for daily net amounts
    
    Args:
        filters: Revenue filter parameters including unitid and optional date range
        
    Returns:
        Heatmap calendar response with data
    """
    try:
        raw_data = ChartService.get_heatmap_calendar_data(
            unitid=filters.unitid,
            start_date=filters.start_date,
            end_date=filters.end_date
        )
        
        # Convert raw data to response model
        heatmap_items = [
            HeatmapCalendarItem(
                date=item['date'],
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]
        
        # Calculate date range
        dates = [item.date for item in heatmap_items]
        date_range = {
            "start_date": min(dates) if dates else date.today(),
            "end_date": max(dates) if dates else date.today()
        }
        
        return HeatmapCalendarResponse(
            data=heatmap_items,
            total_records=len(heatmap_items),
            unitid=filters.unitid,
            date_range=date_range
        )
        
    except Exception as e:
        logger.error(f"Error in heatmap calendar endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sankey-chart", response_model=SankeyChartResponse)
async def get_sankey_chart(filters: RevenueFilterParams):
    """
    Get sankey chart data for patient type to speciality to service flow

    Args:
        filters: Revenue filter parameters including unitid

    Returns:
        Sankey chart response with data
    """
    try:
        raw_data = ChartService.get_sankey_chart_data(unitid=filters.unitid)

        # Convert raw data to response model
        sankey_items = [
            SankeyChartItem(
                source=item['source'],
                target=item['target'],
                value=int(item['value'])
            )
            for item in raw_data
        ]

        # Extract unique nodes
        nodes = list(set([item.source for item in sankey_items] + [item.target for item in sankey_items]))

        return SankeyChartResponse(
            data=sankey_items,
            total_records=len(sankey_items),
            unitid=filters.unitid,
            nodes=nodes
        )

    except Exception as e:
        logger.error(f"Error in sankey chart endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/radar-chart", response_model=RadarChartResponse)
async def get_radar_chart(filters: RevenueFilterParams):
    """
    Get radar chart data for multiple KPIs per department

    Args:
        filters: Revenue filter parameters including unitid

    Returns:
        Radar chart response with data
    """
    try:
        raw_data = ChartService.get_radar_chart_data(unitid=filters.unitid)

        # Convert raw data to response model
        radar_items = [
            RadarChartItem(
                service_department=item['service_department'],
                quantity=int(item['quantity']),
                totalamount=float(item['totalamount']),
                concession=float(item['concession']),
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]

        kpi_names = ["quantity", "totalamount", "concession", "netamount"]

        return RadarChartResponse(
            data=radar_items,
            total_records=len(radar_items),
            unitid=filters.unitid,
            kpi_names=kpi_names
        )

    except Exception as e:
        logger.error(f"Error in radar chart endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/box-plot", response_model=BoxPlotResponse)
async def get_box_plot(filters: RevenueFilterParams):
    """
    Get box plot data for distribution of charges by department

    Args:
        filters: Revenue filter parameters including unitid

    Returns:
        Box plot response with data
    """
    try:
        raw_data = ChartService.get_box_plot_data(unitid=filters.unitid)

        # Convert raw data to response model
        box_plot_items = [
            BoxPlotItem(
                service_department=item['service_department'],
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]

        # Extract unique departments
        departments = list(set([item.service_department for item in box_plot_items]))

        return BoxPlotResponse(
            data=box_plot_items,
            total_records=len(box_plot_items),
            unitid=filters.unitid,
            departments=departments
        )

    except Exception as e:
        logger.error(f"Error in box plot endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/waterfall-chart", response_model=WaterfallChartResponse)
async def get_waterfall_chart(filters: RevenueFilterParams):
    """
    Get waterfall chart data for revenue breakdown

    Args:
        filters: Revenue filter parameters including unitid

    Returns:
        Waterfall chart response with data
    """
    try:
        raw_data = ChartService.get_waterfall_chart_data(unitid=filters.unitid)

        # Convert raw data to response model
        waterfall_items = [
            WaterfallChartItem(
                stage=item['stage'],
                amount=float(item['amount'])
            )
            for item in raw_data
        ]

        # Get net amount (should be the last item)
        net_amount = next((item.amount for item in waterfall_items if item.stage == 'Net'), 0)

        return WaterfallChartResponse(
            data=waterfall_items,
            unitid=filters.unitid,
            net_amount=net_amount
        )

    except Exception as e:
        logger.error(f"Error in waterfall chart endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
