"""
FastAPI routes for Monthly Revenue Summary
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging

from models.revenue_models import (
    RevenueFilterParams, MonthlyTrendResponse, DailyTrendResponse,
    MTDComparisonResponse, YTDComparisonResponse, YoYComparisonResponse,
    DoctorSummaryResponse, ServiceSummaryResponse, MonthlySummaryResponse,
    MonthlyTrendItem, DailyTrendItem, MTDComparisonItem, YTDComparisonItem,
    YoYComparisonItem, DoctorSummaryItem, ServiceSummaryItem, MonthlySummaryItem
)
from services.revenue_service import RevenueService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/revenue", tags=["revenue"])

@router.post("/monthly-trend", response_model=MonthlyTrendResponse)
async def get_monthly_trend(filters: RevenueFilterParams):
    """
    Get monthly trend data for revenue analysis
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        Monthly trend response with data
    """
    try:
        raw_data = RevenueService.get_monthly_trend(
            unitid=filters.unitid,
            trantype=filters.trantype.value if filters.trantype else None
        )
        
        # Convert raw data to response model
        trend_items = [
            MonthlyTrendItem(
                month=item['month'],
                trantype=item['trantype'],
                bill_count=item['bill_count'],
                total_netamount=float(item['total_netamount'])
            )
            for item in raw_data
        ]
        
        return MonthlyTrendResponse(
            data=trend_items,
            total_records=len(trend_items),
            unitid=filters.unitid
        )
        
    except Exception as e:
        logger.error(f"Error in monthly trend endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/daily-trend", response_model=DailyTrendResponse)
async def get_daily_trend(filters: RevenueFilterParams):
    """
    Get daily trend data for last 30 days
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        Daily trend response with data
    """
    try:
        raw_data = RevenueService.get_daily_trend(
            unitid=filters.unitid,
            trantype=filters.trantype.value if filters.trantype else None
        )
        
        # Convert raw data to response model
        trend_items = [
            DailyTrendItem(
                day=item['day'],
                trantype=item['trantype'],
                bill_count=item['bill_count'],
                total_netamount=float(item['total_netamount'])
            )
            for item in raw_data
        ]
        
        return DailyTrendResponse(
            data=trend_items,
            total_records=len(trend_items),
            unitid=filters.unitid
        )
        
    except Exception as e:
        logger.error(f"Error in daily trend endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/mtd-comparison", response_model=MTDComparisonResponse)
async def get_mtd_comparison(filters: RevenueFilterParams):
    """
    Get MTD vs Last MTD comparison (same month, previous year)
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        MTD comparison response with data
    """
    try:
        raw_data = RevenueService.get_mtd_comparison(unitid=filters.unitid)
        
        # Convert raw data to response model
        comparison_items = [
            MTDComparisonItem(
                period=item['period'],
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]
        
        current_date = datetime.now()
        
        return MTDComparisonResponse(
            data=comparison_items,
            unitid=filters.unitid,
            comparison_month=current_date.month,
            comparison_year=current_date.year
        )
        
    except Exception as e:
        logger.error(f"Error in MTD comparison endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ytd-comparison", response_model=YTDComparisonResponse)
async def get_ytd_comparison(filters: RevenueFilterParams):
    """
    Get YTD vs Last YTD comparison (Jan to Today)
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        YTD comparison response with data
    """
    try:
        raw_data = RevenueService.get_ytd_comparison(unitid=filters.unitid)
        
        # Convert raw data to response model
        comparison_items = [
            YTDComparisonItem(
                period=item['period'],
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]
        
        current_date = datetime.now()
        
        return YTDComparisonResponse(
            data=comparison_items,
            unitid=filters.unitid,
            comparison_year=current_date.year
        )
        
    except Exception as e:
        logger.error(f"Error in YTD comparison endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/yoy-comparison", response_model=YoYComparisonResponse)
async def get_yoy_comparison(filters: RevenueFilterParams):
    """
    Get Year-over-Year comparison (monthly)
    
    Args:
        filters: Revenue filter parameters including unitid
        
    Returns:
        YoY comparison response with data
    """
    try:
        raw_data = RevenueService.get_yoy_comparison(unitid=filters.unitid)
        
        # Convert raw data to response model
        comparison_items = [
            YoYComparisonItem(
                month_name=item['month_name'],
                year=int(item['year']),
                netamount=float(item['netamount'])
            )
            for item in raw_data
        ]
        
        # Get unique years for metadata
        years_included = sorted(list(set(item.year for item in comparison_items)))
        
        return YoYComparisonResponse(
            data=comparison_items,
            unitid=filters.unitid,
            years_included=years_included
        )
        
    except Exception as e:
        logger.error(f"Error in YoY comparison endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/doctor-summary", response_model=DoctorSummaryResponse)
async def get_doctor_summary(filters: RevenueFilterParams):
    """
    Get doctor-wise summary from materialized view

    Args:
        filters: Revenue filter parameters including unitid and optional date range

    Returns:
        Doctor summary response with data
    """
    try:
        raw_data = RevenueService.get_doctor_summary(
            unitid=filters.unitid,
            start_date=filters.start_date,
            end_date=filters.end_date
        )

        # Convert raw data to response model
        doctor_items = [
            DoctorSummaryItem(
                performingdoctor=item['performingdoctor'],
                performingdoctorspeciality=item['performingdoctorspeciality'],
                bill_month=item['bill_month'],
                netamount=float(item['netamount']),
                totalamount=float(item['totalamount']),
                transaction_count=int(item['transaction_count'])
            )
            for item in raw_data
        ]

        return DoctorSummaryResponse(
            data=doctor_items,
            total_records=len(doctor_items),
            unitid=filters.unitid
        )

    except Exception as e:
        logger.error(f"Error in doctor summary endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/service-summary", response_model=ServiceSummaryResponse)
async def get_service_summary(filters: RevenueFilterParams):
    """
    Get service department summary from materialized view

    Args:
        filters: Revenue filter parameters including unitid and optional date range

    Returns:
        Service summary response with data
    """
    try:
        raw_data = RevenueService.get_service_summary(
            unitid=filters.unitid,
            start_date=filters.start_date,
            end_date=filters.end_date
        )

        # Convert raw data to response model
        service_items = [
            ServiceSummaryItem(
                service_department=item['service_department'],
                bill_month=item['bill_month'],
                netamount=float(item['netamount']),
                quantity=int(item['quantity']),
                bill_count=int(item['bill_count'])
            )
            for item in raw_data
        ]

        return ServiceSummaryResponse(
            data=service_items,
            total_records=len(service_items),
            unitid=filters.unitid
        )

    except Exception as e:
        logger.error(f"Error in service summary endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/monthly-summary", response_model=MonthlySummaryResponse)
async def get_monthly_summary(filters: RevenueFilterParams):
    """
    Get monthly summary from materialized view

    Args:
        filters: Revenue filter parameters including unitid and optional date range

    Returns:
        Monthly summary response with data
    """
    try:
        raw_data = RevenueService.get_monthly_summary(
            unitid=filters.unitid,
            start_date=filters.start_date,
            end_date=filters.end_date
        )

        # Convert raw data to response model
        monthly_items = [
            MonthlySummaryItem(
                bill_month=item['bill_month'],
                unitid=item['unitid'],
                performingdoctor=item.get('performingdoctor'),
                performingdoctorspeciality=item.get('performingdoctorspeciality'),
                service_department=item.get('service_department'),
                visittype=item.get('visittype'),
                payertype=item.get('payertype'),
                payer=item.get('payer'),
                trantype=item['trantype'],
                bill_count=int(item['bill_count']),
                quantity=int(item['quantity']),
                totalamount=float(item['totalamount']),
                concessionamount=float(item['concessionamount']),
                netamount=float(item['netamount']),
                selfamount=float(item['selfamount']),
                nonselfamount=float(item['nonselfamount'])
            )
            for item in raw_data
        ]

        return MonthlySummaryResponse(
            data=monthly_items,
            total_records=len(monthly_items),
            unitid=filters.unitid
        )

    except Exception as e:
        logger.error(f"Error in monthly summary endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
