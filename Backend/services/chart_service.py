"""
Chart Service for BI Dashboard Charts
Implements queries from BI Scripts for various chart types using ClickHouse
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging
from services.clickhouse_service import clickhouse_service

logger = logging.getLogger(__name__)

class ChartService:
    """Service for chart-specific analytics operations"""
    
    @staticmethod
    def get_pie_chart_data(unitid: str, chart_type: str = "payertype") -> List[Dict[str, Any]]:
        """
        Get pie chart data for payertype or unit-wise share
        
        Args:
            unitid: Unit ID to filter data
            chart_type: Type of pie chart (payertype, unit, etc.)
            
        Returns:
            List of pie chart data
        """
        if chart_type == "payertype":
            query = """
            SELECT
                payertype AS category,
                SUM(netamount) AS value
            FROM mv_opdrevenue
            WHERE unitid = {unitid:String}
            GROUP BY 1
            ORDER BY 2 DESC
            """
        else:
            # Default to payertype if unknown type
            query = """
            SELECT
                payertype AS category,
                SUM(netamount) AS value
            FROM mv_opdrevenue
            WHERE unitid = {unitid:String}
            GROUP BY 1
            ORDER BY 2 DESC
            """
        
        parameters = {"unitid": unitid}
        
        try:
            raw_data = clickhouse_service.execute_query(query, parameters)
            
            # Calculate total and percentages
            total_value = sum(item['value'] for item in raw_data)
            
            # Add percentage calculation
            for item in raw_data:
                item['percentage'] = (item['value'] / total_value * 100) if total_value > 0 else 0
                
            return raw_data, total_value
        except Exception as e:
            logger.error(f"Error getting pie chart data: {str(e)}")
            raise
    
    @staticmethod
    def get_treemap_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get treemap data for Service Department → Doctor → Net Revenue
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of treemap data
        """
        query = """
        SELECT
            service_department,
            performingdoctor,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        GROUP BY 1, 2
        ORDER BY 3 DESC
        """
        
        parameters = {"unitid": unitid}
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting treemap data: {str(e)}")
            raise
    
    @staticmethod
    def get_scatter_plot_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get scatter plot data for Rate vs Quantity by Service
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of scatter plot data
        """
        query = """
        SELECT
            service_name,
            rate,
            quantity
        FROM mv_opdrevenue
        WHERE quantity IS NOT NULL 
        AND rate IS NOT NULL
        AND unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting scatter plot data: {str(e)}")
            raise
    
    @staticmethod
    def get_histogram_data(unitid: str, bucket_count: int = 10, max_amount: float = 10000) -> List[Dict[str, Any]]:
        """
        Get histogram data for distribution of net amounts
        
        Args:
            unitid: Unit ID to filter data
            bucket_count: Number of buckets for histogram
            max_amount: Maximum amount to consider
            
        Returns:
            List of histogram data
        """
        query = """
        SELECT
            width_bucket(netamount, 0, {max_amount:Float64}, {bucket_count:UInt32}) AS bucket,
            COUNT(*) AS frequency
        FROM mv_opdrevenue
        WHERE netamount BETWEEN 0 AND {max_amount:Float64}
        AND unitid = {unitid:String}
        GROUP BY 1
        ORDER BY 1
        """
        
        parameters = {
            "unitid": unitid,
            "bucket_count": bucket_count,
            "max_amount": max_amount
        }
        
        try:
            raw_data = clickhouse_service.execute_query(query, parameters)
            
            # Add range information
            bucket_size = max_amount / bucket_count
            for item in raw_data:
                bucket_num = item['bucket']
                item['range_start'] = (bucket_num - 1) * bucket_size
                item['range_end'] = bucket_num * bucket_size
                
            return raw_data
        except Exception as e:
            logger.error(f"Error getting histogram data: {str(e)}")
            raise
    
    @staticmethod
    def get_pareto_chart_data(unitid: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get pareto chart data for top services contributing to revenue
        
        Args:
            unitid: Unit ID to filter data
            limit: Number of top services to return
            
        Returns:
            List of pareto chart data with cumulative percentages
        """
        query = """
        SELECT
            service_name,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        GROUP BY 1
        ORDER BY 2 DESC
        LIMIT {limit:UInt32}
        """
        
        parameters = {"unitid": unitid, "limit": limit}
        
        try:
            raw_data = clickhouse_service.execute_query(query, parameters)
            
            # Calculate cumulative percentages
            total_amount = sum(item['netamount'] for item in raw_data)
            cumulative_amount = 0
            
            for item in raw_data:
                cumulative_amount += item['netamount']
                item['cumulative_percentage'] = (cumulative_amount / total_amount * 100) if total_amount > 0 else 0
                
            return raw_data, total_amount
        except Exception as e:
            logger.error(f"Error getting pareto chart data: {str(e)}")
            raise
    
    @staticmethod
    def get_gauge_chart_data(unitid: str, target_mtd: float = 2000000) -> Dict[str, Any]:
        """
        Get gauge chart data for current MTD vs target
        
        Args:
            unitid: Unit ID to filter data
            target_mtd: Target MTD amount (default: 2,000,000)
            
        Returns:
            Dictionary with gauge chart data
        """
        query = """
        SELECT
            SUM(netamount) AS current_mtd
        FROM mv_opdrevenue
        WHERE billdate >= DATE_TRUNC('month', CURRENT_DATE)
        AND unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        try:
            result = clickhouse_service.execute_query(query, parameters)
            current_mtd = result[0]['current_mtd'] if result else 0
            
            return {
                'current_mtd': current_mtd,
                'target_mtd': target_mtd,
                'percentage': (current_mtd / target_mtd * 100) if target_mtd > 0 else 0
            }
        except Exception as e:
            logger.error(f"Error getting gauge chart data: {str(e)}")
            raise
    
    @staticmethod
    def get_heatmap_calendar_data(unitid: str, start_date: Optional[date] = None, 
                                 end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get heatmap calendar data for daily net amounts
        
        Args:
            unitid: Unit ID to filter data
            start_date: Start date for filtering (optional)
            end_date: End date for filtering (optional)
            
        Returns:
            List of heatmap calendar data
        """
        query = """
        SELECT
            billdate::date AS date,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if start_date:
            query += " AND billdate >= {start_date:Date}"
            parameters["start_date"] = start_date
        
        if end_date:
            query += " AND billdate <= {end_date:Date}"
            parameters["end_date"] = end_date
            
        query += """
        GROUP BY 1
        ORDER BY 1
        """
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting heatmap calendar data: {str(e)}")
            raise
    
    @staticmethod
    def get_sankey_chart_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get sankey chart data for patient type to speciality to service flow
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of sankey chart data
        """
        query = """
        SELECT
            patient_type AS source,
            performingdoctorspeciality AS target,
            COUNT(*) AS value
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        AND patient_type IS NOT NULL
        AND performingdoctorspeciality IS NOT NULL
        GROUP BY 1, 2
        ORDER BY 3 DESC
        """
        
        parameters = {"unitid": unitid}
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting sankey chart data: {str(e)}")
            raise

    @staticmethod
    def get_radar_chart_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get radar chart data for multiple KPIs per department

        Args:
            unitid: Unit ID to filter data

        Returns:
            List of radar chart data
        """
        query = """
        SELECT
            service_department,
            SUM(quantity) AS quantity,
            SUM(totalamount) AS totalamount,
            SUM(concessionamount) AS concession,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        GROUP BY 1
        ORDER BY 5 DESC
        """

        parameters = {"unitid": unitid}

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting radar chart data: {str(e)}")
            raise

    @staticmethod
    def get_box_plot_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get box plot data for distribution of charges by department

        Args:
            unitid: Unit ID to filter data

        Returns:
            List of box plot data
        """
        query = """
        SELECT
            service_department,
            netamount
        FROM mv_opdrevenue
        WHERE netamount > 0
        AND unitid = {unitid:String}
        ORDER BY 1, 2
        """

        parameters = {"unitid": unitid}

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting box plot data: {str(e)}")
            raise

    @staticmethod
    def get_waterfall_chart_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get waterfall chart data for revenue breakdown

        Args:
            unitid: Unit ID to filter data

        Returns:
            List of waterfall chart data
        """
        query = """
        SELECT 'Total' AS stage, SUM(totalamount) AS amount
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        UNION ALL
        SELECT 'Concession', -SUM(concessionamount)
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        UNION ALL
        SELECT 'Net', SUM(netamount)
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        """

        parameters = {"unitid": unitid}

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting waterfall chart data: {str(e)}")
            raise
