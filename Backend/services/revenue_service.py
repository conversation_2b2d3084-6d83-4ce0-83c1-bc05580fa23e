"""
Revenue Service for Monthly Revenue Summary
Implements queries for MTD, YTD, YoY analysis using ClickHouse
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging
from services.clickhouse_service import clickhouse_service
from models.revenue_models import (
    RevenueFilterParams, MonthlyTrendResponse, DailyTrendResponse,
    MTDComparisonResponse, YTDComparisonResponse, YoYComparisonResponse,
    DoctorSummaryResponse, ServiceSummaryResponse, MonthlySummaryResponse
)

logger = logging.getLogger(__name__)

class RevenueService:
    """Service for revenue analytics and monthly summary operations"""
    
    @staticmethod
    def get_monthly_trend(unitid: str, trantype: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get monthly trend data
        
        Args:
            unitid: Unit ID to filter data
            trantype: Optional transaction type filter
            
        Returns:
            List of monthly trend data
        """
        query = """
        SELECT 
            DATE_TRUNC('month', billdate) AS month,
            trantype,
            COUNT(DISTINCT billno) AS bill_count,
            SUM(netamount) AS total_netamount
        FROM mv_opdrevenue
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if trantype:
            query += " AND trantype = {trantype:String}"
            parameters["trantype"] = trantype
            
        query += """
        GROUP BY 1, 2
        ORDER BY 1, 2
        """
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting monthly trend: {str(e)}")
            raise
    
    @staticmethod
    def get_daily_trend(unitid: str, trantype: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get daily trend data for last 30 days
        
        Args:
            unitid: Unit ID to filter data
            trantype: Optional transaction type filter
            
        Returns:
            List of daily trend data
        """
        query = """
        SELECT 
            billdate::date AS day,
            trantype,
            COUNT(DISTINCT billno) AS bill_count,
            SUM(netamount) AS total_netamount
        FROM mv_opdrevenue
        WHERE billdate >= CURRENT_DATE - INTERVAL '30 days'
        AND unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if trantype:
            query += " AND trantype = {trantype:String}"
            parameters["trantype"] = trantype
            
        query += """
        GROUP BY 1, 2
        ORDER BY 1, 2
        """
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting daily trend: {str(e)}")
            raise
    
    @staticmethod
    def get_mtd_comparison(unitid: str) -> List[Dict[str, Any]]:
        """
        Get MTD vs Last MTD comparison (same month, previous year)
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of MTD comparison data
        """
        query = """
        SELECT 
            CASE 
                WHEN EXTRACT(YEAR FROM billdate) = EXTRACT(YEAR FROM CURRENT_DATE) THEN 'Current MTD'
                ELSE 'Last Year MTD'
            END AS period,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE EXTRACT(MONTH FROM billdate) = EXTRACT(MONTH FROM CURRENT_DATE)
          AND EXTRACT(DAY FROM billdate) <= EXTRACT(DAY FROM CURRENT_DATE)
          AND EXTRACT(YEAR FROM billdate) IN (EXTRACT(YEAR FROM CURRENT_DATE), EXTRACT(YEAR FROM CURRENT_DATE) - 1)
          AND unitid = {unitid:String}
        GROUP BY 1
        """
        
        parameters = {"unitid": unitid}
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting MTD comparison: {str(e)}")
            raise
    
    @staticmethod
    def get_ytd_comparison(unitid: str) -> List[Dict[str, Any]]:
        """
        Get YTD vs Last YTD comparison (Jan to Today)
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of YTD comparison data
        """
        query = """
        SELECT 
            CASE 
                WHEN EXTRACT(YEAR FROM billdate) = EXTRACT(YEAR FROM CURRENT_DATE) THEN 'Current YTD'
                ELSE 'Last Year YTD'
            END AS period,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE billdate::date <= CURRENT_DATE
          AND billdate::date >= (CURRENT_DATE - INTERVAL '1 year')::date
          AND (
                (EXTRACT(YEAR FROM billdate) = EXTRACT(YEAR FROM CURRENT_DATE)
                 AND billdate >= DATE_TRUNC('year', CURRENT_DATE))
             OR
                (EXTRACT(YEAR FROM billdate) = EXTRACT(YEAR FROM CURRENT_DATE) - 1
                 AND billdate >= DATE_TRUNC('year', CURRENT_DATE - INTERVAL '1 year')
                 AND billdate <= DATE_TRUNC('day', CURRENT_DATE - INTERVAL '1 year'))
              )
          AND unitid = {unitid:String}
        GROUP BY 1
        """
        
        parameters = {"unitid": unitid}
        
        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting YTD comparison: {str(e)}")
            raise

    @staticmethod
    def get_yoy_comparison(unitid: str) -> List[Dict[str, Any]]:
        """
        Get Year-over-Year comparison (monthly)

        Args:
            unitid: Unit ID to filter data

        Returns:
            List of YoY comparison data
        """
        query = """
        SELECT
            TO_CHAR(billdate, 'Mon') AS month_name,
            EXTRACT(YEAR FROM billdate) AS year,
            SUM(netamount) AS netamount
        FROM mv_opdrevenue
        WHERE billdate >= CURRENT_DATE - INTERVAL '2 year'
        AND unitid = {unitid:String}
        GROUP BY 1, 2
        ORDER BY 1, 2
        """

        parameters = {"unitid": unitid}

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting YoY comparison: {str(e)}")
            raise

    @staticmethod
    def get_doctor_summary(unitid: str, start_date: Optional[date] = None,
                          end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get doctor-wise summary from materialized view

        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            List of doctor summary data
        """
        query = """
        SELECT
            performingdoctor,
            performingdoctorspeciality,
            bill_month,
            SUM(netamount) AS netamount,
            SUM(totalamount) AS totalamount,
            COUNT(*) AS transaction_count
        FROM mv_opd_doctor_summary
        WHERE 1=1
        """

        parameters = {}

        if unitid:
            # Note: Assuming unitid is available in the materialized view
            # If not, this would need to be joined with the main table
            query += " AND unitid = {unitid:String}"
            parameters["unitid"] = unitid

        if start_date:
            query += " AND bill_month >= {start_date:Date}"
            parameters["start_date"] = start_date

        if end_date:
            query += " AND bill_month <= {end_date:Date}"
            parameters["end_date"] = end_date

        query += """
        GROUP BY 1, 2, 3
        ORDER BY bill_month DESC, netamount DESC
        """

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting doctor summary: {str(e)}")
            raise

    @staticmethod
    def get_service_summary(unitid: str, start_date: Optional[date] = None,
                           end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get service department summary from materialized view

        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            List of service summary data
        """
        query = """
        SELECT
            service_department,
            bill_month,
            SUM(netamount) AS netamount,
            SUM(quantity) AS quantity,
            COUNT(DISTINCT bill_count) AS bill_count
        FROM mv_opd_service_summary
        WHERE 1=1
        """

        parameters = {}

        if unitid:
            # Note: Assuming unitid is available in the materialized view
            # If not, this would need to be joined with the main table
            query += " AND unitid = {unitid:String}"
            parameters["unitid"] = unitid

        if start_date:
            query += " AND bill_month >= {start_date:Date}"
            parameters["start_date"] = start_date

        if end_date:
            query += " AND bill_month <= {end_date:Date}"
            parameters["end_date"] = end_date

        query += """
        GROUP BY 1, 2
        ORDER BY bill_month DESC, netamount DESC
        """

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting service summary: {str(e)}")
            raise

    @staticmethod
    def get_monthly_summary(unitid: str, start_date: Optional[date] = None,
                           end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get monthly summary from materialized view

        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            List of monthly summary data
        """
        query = """
        SELECT
            bill_month,
            unitid,
            performingdoctor,
            performingdoctorspeciality,
            service_department,
            visittype,
            payertype,
            payer,
            trantype,
            bill_count,
            quantity,
            totalamount,
            concessionamount,
            netamount,
            selfamount,
            nonselfamount
        FROM mv_opd_monthly_summary
        WHERE unitid = {unitid:String}
        """

        parameters = {"unitid": unitid}

        if start_date:
            query += " AND bill_month >= {start_date:Date}"
            parameters["start_date"] = start_date

        if end_date:
            query += " AND bill_month <= {end_date:Date}"
            parameters["end_date"] = end_date

        query += """
        ORDER BY bill_month DESC
        """

        try:
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting monthly summary: {str(e)}")
            raise
