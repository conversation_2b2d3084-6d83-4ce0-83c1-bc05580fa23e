"""
FastAPI backend for Hospital Dashboard
Provides REST API endpoints for data access while maintaining existing DuckDB logic
Enhanced with ClickHouse integration for Monthly Revenue Summary
"""
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime, date
import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add parent directory to path to import existing modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from loaddata_duckdb import (
    get_units, get_admissions, get_discharges,
    get_opd_revenue, get_discharge_timelines
)
from services.analytics_service import AnalyticsService
try:
    from routes.revenue_routes import router as revenue_router
    from routes.chart_routes import router as chart_router
    from services.clickhouse_service import clickhouse_service
    CLICKHOUSE_ENABLED = True
except ImportError as e:
    logger.warning(f"ClickHouse integration disabled due to import error: {e}")
    revenue_router = None
    chart_router = None
    clickhouse_service = None
    CLICKHOUSE_ENABLED = False

app = FastAPI(
    title="Hospital Dashboard API",
    description="REST API for hospital analytics dashboard with ClickHouse integration",
    version="2.0.0"
)

# Enable CORS for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8083", "http://localhost:8081"],  # React and Expo ports
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include revenue and chart routes if available
if CLICKHOUSE_ENABLED and revenue_router:
    app.include_router(revenue_router)
if CLICKHOUSE_ENABLED and chart_router:
    app.include_router(chart_router)

# Pydantic models for request/response
class FilterParams(BaseModel):
    unit_ids: Optional[List[str]] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    year: Optional[int] = None
    quarter: Optional[str] = None

class DataResponse(BaseModel):
    data: List[Dict[str, Any]]
    columns: List[str]
    total_records: int

class MetricsResponse(BaseModel):
    total_count: int
    average_daily: float
    busiest_day: Optional[str] = None
    additional_metrics: Dict[str, Any] = {}

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Hospital Dashboard API is running", "version": "2.0.0"}

@app.get("/health/clickhouse")
async def clickhouse_health():
    """ClickHouse connection health check"""
    if not CLICKHOUSE_ENABLED:
        return {"status": "disabled", "message": "ClickHouse integration is disabled"}

    try:
        is_connected = clickhouse_service.test_connection()
        if is_connected:
            return {"status": "healthy", "message": "ClickHouse connection successful"}
        else:
            return {"status": "unhealthy", "message": "ClickHouse connection failed"}
    except Exception as e:
        logger.error(f"ClickHouse health check failed: {str(e)}")
        return {"status": "unhealthy", "message": f"ClickHouse error: {str(e)}"}

@app.get("/api/units", response_model=DataResponse)
async def get_units_endpoint():
    """Get all hospital units"""
    try:
        rows, cols = get_units()
        data = [dict(zip(cols, row)) for row in rows]
        return DataResponse(
            data=data,
            columns=cols,
            total_records=len(data)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/admissions", response_model=DataResponse)
async def get_admissions_endpoint(filters: FilterParams):
    """Get admissions data with filters"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_admissions(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        return DataResponse(
            data=data,
            columns=cols,
            total_records=len(data)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/discharges", response_model=DataResponse)
async def get_discharges_endpoint(filters: FilterParams):
    """Get discharges data with filters"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_discharges(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        return DataResponse(
            data=data,
            columns=cols,
            total_records=len(data)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/revenue", response_model=DataResponse)
async def get_revenue_endpoint(filters: FilterParams):
    """Get IPD revenue data with filters"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_opd_revenue(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        return DataResponse(
            data=data,
            columns=cols,
            total_records=len(data)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/discharge-timelines", response_model=DataResponse)
async def get_discharge_timelines_endpoint(filters: FilterParams):
    """Get discharge timelines data with filters"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_discharge_timelines(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        return DataResponse(
            data=data,
            columns=cols,
            total_records=len(data)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/admissions/analytics")
async def get_admissions_analytics(filters: FilterParams):
    """Get computed admission analytics and metrics"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_admissions(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        metrics = AnalyticsService.compute_admission_metrics(data)
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/revenue/analytics")
async def get_revenue_analytics(filters: FilterParams):
    """Get computed revenue analytics and metrics"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_opd_revenue(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        metrics = AnalyticsService.compute_revenue_metrics(data)
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/discharges/analytics")
async def get_discharges_analytics(filters: FilterParams):
    """Get computed discharge analytics and metrics"""
    try:
        filter_dict = filters.dict(exclude_none=True)
        rows, cols = get_discharges(**filter_dict)
        data = [dict(zip(cols, row)) for row in rows]
        metrics = AnalyticsService.compute_discharge_metrics(data)
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
