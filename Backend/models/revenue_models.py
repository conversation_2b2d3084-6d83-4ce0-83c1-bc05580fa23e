"""
Pydantic models for Monthly Revenue Summary API
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
import datetime as dt
from enum import Enum

class TranType(str, Enum):
    """Transaction type enumeration"""
    OPD = "OPD"
    IPD = "IPD"
    EMERGENCY = "EMERGENCY"

class PeriodType(str, Enum):
    """Period type for comparisons"""
    CURRENT_MTD = "Current MTD"
    LAST_YEAR_MTD = "Last Year MTD"
    CURRENT_YTD = "Current YTD"
    LAST_YEAR_YTD = "Last Year YTD"

class RevenueFilterParams(BaseModel):
    """Filter parameters for revenue queries"""
    unitid: str = Field(..., description="Unit ID to filter data")
    start_date: Optional[dt.date] = Field(None, description="Start date for filtering")
    end_date: Optional[dt.date] = Field(None, description="End date for filtering")
    trantype: Optional[TranType] = Field(None, description="Transaction type filter")
    
class MonthlyTrendItem(BaseModel):
    """Monthly trend data item"""
    month: dt.date = Field(..., description="Month (first day of month)")
    trantype: str = Field(..., description="Transaction type")
    bill_count: int = Field(..., description="Number of bills")
    total_netamount: float = Field(..., description="Total net amount")

class MonthlyTrendResponse(BaseModel):
    """Response for monthly trend data"""
    data: List[MonthlyTrendItem]
    total_records: int
    unitid: str

class DailyTrendItem(BaseModel):
    """Daily trend data item"""
    day: dt.date = Field(..., description="Date")
    trantype: str = Field(..., description="Transaction type")
    bill_count: int = Field(..., description="Number of bills")
    total_netamount: float = Field(..., description="Total net amount")

class DailyTrendResponse(BaseModel):
    """Response for daily trend data (last 30 days)"""
    data: List[DailyTrendItem]
    total_records: int
    unitid: str

class MTDComparisonItem(BaseModel):
    """MTD comparison data item"""
    period: PeriodType = Field(..., description="Period type")
    netamount: float = Field(..., description="Net amount")

class MTDComparisonResponse(BaseModel):
    """Response for MTD vs Last MTD comparison"""
    data: List[MTDComparisonItem]
    unitid: str
    comparison_month: int
    comparison_year: int

class YTDComparisonItem(BaseModel):
    """YTD comparison data item"""
    period: PeriodType = Field(..., description="Period type")
    netamount: float = Field(..., description="Net amount")

class YTDComparisonResponse(BaseModel):
    """Response for YTD vs Last YTD comparison"""
    data: List[YTDComparisonItem]
    unitid: str
    comparison_year: int

class YoYComparisonItem(BaseModel):
    """Year-over-Year comparison data item"""
    month_name: str = Field(..., description="Month name (e.g., 'Jan', 'Feb')")
    year: int = Field(..., description="Year")
    netamount: float = Field(..., description="Net amount")

class YoYComparisonResponse(BaseModel):
    """Response for Year-over-Year comparison (monthly)"""
    data: List[YoYComparisonItem]
    unitid: str
    years_included: List[int]

class DoctorSummaryItem(BaseModel):
    """Doctor-wise summary data item"""
    performingdoctor: str = Field(..., description="Performing doctor name")
    performingdoctorspeciality: str = Field(..., description="Doctor speciality")
    bill_month: dt.date = Field(..., description="Bill month")
    netamount: float = Field(..., description="Net amount")
    totalamount: float = Field(..., description="Total amount")
    transaction_count: int = Field(..., description="Number of transactions")

class DoctorSummaryResponse(BaseModel):
    """Response for doctor-wise summary"""
    data: List[DoctorSummaryItem]
    total_records: int
    unitid: str

class ServiceSummaryItem(BaseModel):
    """Service department summary data item"""
    service_department: str = Field(..., description="Service department")
    bill_month: dt.date = Field(..., description="Bill month")
    netamount: float = Field(..., description="Net amount")
    quantity: int = Field(..., description="Quantity")
    bill_count: int = Field(..., description="Number of bills")

class ServiceSummaryResponse(BaseModel):
    """Response for service department summary"""
    data: List[ServiceSummaryItem]
    total_records: int
    unitid: str

class MonthlySummaryItem(BaseModel):
    """Monthly summary data item from materialized view"""
    bill_month: dt.date = Field(..., description="Bill month")
    unitid: str = Field(..., description="Unit ID")
    performingdoctor: Optional[str] = Field(None, description="Performing doctor")
    performingdoctorspeciality: Optional[str] = Field(None, description="Doctor speciality")
    service_department: Optional[str] = Field(None, description="Service department")
    visittype: Optional[str] = Field(None, description="Visit type")
    payertype: Optional[str] = Field(None, description="Payer type")
    payer: Optional[str] = Field(None, description="Payer")
    trantype: str = Field(..., description="Transaction type")
    bill_count: int = Field(..., description="Number of bills")
    quantity: int = Field(..., description="Quantity")
    totalamount: float = Field(..., description="Total amount")
    concessionamount: float = Field(..., description="Concession amount")
    netamount: float = Field(..., description="Net amount")
    selfamount: float = Field(..., description="Self amount")
    nonselfamount: float = Field(..., description="Non-self amount")

class MonthlySummaryResponse(BaseModel):
    """Response for monthly summary from materialized view"""
    data: List[MonthlySummaryItem]
    total_records: int
    unitid: str

# ============================================================================
# NEW CHART-SPECIFIC MODELS (from BI Scripts)
# ============================================================================

# Pie Chart Models - Payertype/Unit-wise Share
class PieChartItem(BaseModel):
    """Individual pie chart data item"""
    category: str = Field(..., description="Category name (payertype, unit, etc.)")
    value: float = Field(..., description="Value for the category")
    percentage: Optional[float] = Field(None, description="Percentage of total")

class PieChartResponse(BaseModel):
    """Response model for pie chart data"""
    data: List[PieChartItem] = Field(..., description="List of pie chart items")
    total_value: float = Field(..., description="Total value across all categories")
    unitid: str = Field(..., description="Unit ID used for filtering")
    chart_type: str = Field(..., description="Type of pie chart (payertype, unit, etc.)")

# Treemap Models - Service Department → Doctor → Net Revenue
class TreemapItem(BaseModel):
    """Individual treemap data item"""
    service_department: str = Field(..., description="Service department name")
    performingdoctor: str = Field(..., description="Performing doctor name")
    netamount: float = Field(..., description="Net amount")

class TreemapResponse(BaseModel):
    """Response model for treemap data"""
    data: List[TreemapItem] = Field(..., description="List of treemap items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")

# Scatter Plot Models - Rate vs Quantity by Service
class ScatterPlotItem(BaseModel):
    """Individual scatter plot data item"""
    service_name: str = Field(..., description="Service name")
    rate: float = Field(..., description="Rate")
    quantity: int = Field(..., description="Quantity")

class ScatterPlotResponse(BaseModel):
    """Response model for scatter plot data"""
    data: List[ScatterPlotItem] = Field(..., description="List of scatter plot items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")

# Histogram Models - Distribution of Net Amounts
class HistogramItem(BaseModel):
    """Individual histogram data item"""
    bucket: int = Field(..., description="Bucket number")
    frequency: int = Field(..., description="Frequency count")
    range_start: float = Field(..., description="Range start value")
    range_end: float = Field(..., description="Range end value")

class HistogramResponse(BaseModel):
    """Response model for histogram data"""
    data: List[HistogramItem] = Field(..., description="List of histogram items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")
    bucket_count: int = Field(..., description="Number of buckets")

# Pareto Chart Models - Top Services Contributing to Revenue
class ParetoChartItem(BaseModel):
    """Individual pareto chart data item"""
    service_name: str = Field(..., description="Service name")
    netamount: float = Field(..., description="Net amount")
    cumulative_percentage: Optional[float] = Field(None, description="Cumulative percentage")

class ParetoChartResponse(BaseModel):
    """Response model for pareto chart data"""
    data: List[ParetoChartItem] = Field(..., description="List of pareto chart items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")
    total_amount: float = Field(..., description="Total amount across all services")

# Gauge Chart Models - Current MTD vs Target
class GaugeChartItem(BaseModel):
    """Individual gauge chart data item"""
    current_mtd: float = Field(..., description="Current MTD value")
    target_mtd: float = Field(..., description="Target MTD value")
    percentage: float = Field(..., description="Percentage of target achieved")

class GaugeChartResponse(BaseModel):
    """Response model for gauge chart data"""
    data: GaugeChartItem = Field(..., description="Gauge chart data")
    unitid: str = Field(..., description="Unit ID used for filtering")
    month: int = Field(..., description="Current month")
    year: int = Field(..., description="Current year")

# Heatmap Calendar Models - Daily Net Amount for Heatmap
class HeatmapCalendarItem(BaseModel):
    """Individual heatmap calendar data item"""
    date: dt.date = Field(..., description="Date")
    netamount: float = Field(..., description="Net amount for the date")

class HeatmapCalendarResponse(BaseModel):
    """Response model for heatmap calendar data"""
    data: List[HeatmapCalendarItem] = Field(..., description="List of heatmap calendar items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")
    date_range: Dict[str, dt.date] = Field(..., description="Date range (start_date, end_date)")

# Sankey Chart Models - Patient Type to Speciality to Service Flow
class SankeyChartItem(BaseModel):
    """Individual sankey chart data item"""
    source: str = Field(..., description="Source node")
    target: str = Field(..., description="Target node")
    value: int = Field(..., description="Flow value")

class SankeyChartResponse(BaseModel):
    """Response model for sankey chart data"""
    data: List[SankeyChartItem] = Field(..., description="List of sankey chart items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")
    nodes: List[str] = Field(..., description="List of all unique nodes")

# Radar Chart Models - Multiple KPIs per Department
class RadarChartItem(BaseModel):
    """Individual radar chart data item"""
    service_department: str = Field(..., description="Service department")
    quantity: int = Field(..., description="Total quantity")
    totalamount: float = Field(..., description="Total amount")
    concession: float = Field(..., description="Concession amount")
    netamount: float = Field(..., description="Net amount")

class RadarChartResponse(BaseModel):
    """Response model for radar chart data"""
    data: List[RadarChartItem] = Field(..., description="List of radar chart items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")
    kpi_names: List[str] = Field(..., description="List of KPI names")

# Box Plot Models - Distribution of Charges by Department
class BoxPlotItem(BaseModel):
    """Individual box plot data item"""
    service_department: str = Field(..., description="Service department")
    netamount: float = Field(..., description="Net amount")

class BoxPlotResponse(BaseModel):
    """Response model for box plot data"""
    data: List[BoxPlotItem] = Field(..., description="List of box plot items")
    total_records: int = Field(..., description="Total number of records")
    unitid: str = Field(..., description="Unit ID used for filtering")
    departments: List[str] = Field(..., description="List of unique departments")

# Waterfall Chart Models - Revenue Breakdown
class WaterfallChartItem(BaseModel):
    """Individual waterfall chart data item"""
    stage: str = Field(..., description="Stage name (Total, Concession, Net)")
    amount: float = Field(..., description="Amount for the stage")

class WaterfallChartResponse(BaseModel):
    """Response model for waterfall chart data"""
    data: List[WaterfallChartItem] = Field(..., description="List of waterfall chart items")
    unitid: str = Field(..., description="Unit ID used for filtering")
    net_amount: float = Field(..., description="Final net amount")
